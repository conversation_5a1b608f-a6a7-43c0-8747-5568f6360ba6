
#include <stdio.h>
#include <stdlib.h>

int factorial(int n);

int main(int argc, char *argv[]) {
	if (argc != 2) {
		fprintf(stderr, "usage: %s <number>\n", argv[0]);
		exit(EXIT_FAILURE);
	}

	int num = atoi(argv[1]);
	printf("%d! = %d\n", num, factorial(num));
}

int factorial(int n) {
	// printf("factorial(%d): address of n is %p\n", n, &n);
	printf("factorial(%d) entered\n", n);
	int res;
	if (n == 0) {
		res =  1;
	} else {
		res = n * factorial(n - 1);
	}
	printf("factorial(%d) returning %d\n", n, res);
	return res;
}
