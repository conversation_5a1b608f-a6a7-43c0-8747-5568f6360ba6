
CC = clang
CFLAGS = -Wall -Werror -g -fsanitize=address,leak,undefined

all: factorial listSum listPrintReverse listPrintEverySecondValue listGetIndex listAppend listCopy listDelete listAppend2 printNumberedList

factorial: factorial.c

listSum: listSum.c
listPrintReverse: listPrintReverse.c
listPrintEverySecondValue: listPrintEverySecondValue.c
listGetIndex: listGetIndex.c
listAppend: listAppend.c
listCopy: listCopy.c
listDelete: listDelete.c

listAppend2: listAppend2.c
printNumberedList: printNumberedList.c

clean:
	rm -rf *.dSYM
	rm -f factorial listSum listPrintReverse listPrintEverySecondValue listGetIndex listAppend listCopy listDelete listAppend2 printNumberedList
