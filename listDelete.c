
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define MAX_LINE 1024

struct node {
	int value;
	struct node *next;
};

struct node *listDelete(struct node *list, int value);

struct node *readList(void);
struct node *newNode(int value);
void printList(struct node *list);
void freeList(struct node *list);

int main(void) {
	printf("Enter list values: ");
	struct node *list = readList();

	printf("List: ");
	printList(list);

	printf("Enter value: ");
	int value = 0;
	scanf("%d", &value);

	list = listDelete(list, value);
	printf("List after deleting %d: ", value);
	printList(list);

	freeList(list);
}

////////////////////////////////////////////////////////////////////////

struct node *listDelete(struct node *list, int value) {
	if (list == NULL) {
		return NULL;
	} else if (list->value == value) {
		struct node *newHead = list->next;
		free(list);
		return newHead;
	} else {
		list->next = listDelete(list->next, value);
		return list;
	}
}

////////////////////////////////////////////////////////////////////////

struct node *readList(void) {
	char line[MAX_LINE];
	fgets(line, MAX_LINE, stdin);

	struct node *list = NULL;
	struct node *curr = NULL;

	char *token = strtok(line, " \n\t");
	while (token != NULL) {
		int value;
		if (sscanf(token, "%d", &value) == 1) {
			struct node *node = newNode(value);
			if (list == NULL) {
				list = node;
			} else {
				curr->next = node;
			}
			curr = node;
		}

		token = strtok(NULL, " \n\t");
	}

	return list;
}

struct node *newNode(int value) {
	struct node *n = malloc(sizeof(*n));
	if (n == NULL) {
		fprintf(stderr, "error: out of memory\n");
		exit(EXIT_FAILURE);
	}

	n->value = value;
	n->next = NULL;
	return n;
}

void printList(struct node *list) {
	printf("[");
	for (struct node *curr = list; curr != NULL; curr = curr->next) {
		printf("%d", curr->value);
		if (curr->next != NULL) {
			printf(", ");
		}
	}
	printf("]\n");
}

void freeList(struct node *list) {
	struct node *curr = list;
	while (curr != NULL) {
		struct node *temp = curr;
		curr = curr->next;
		free(temp);
	}
}
